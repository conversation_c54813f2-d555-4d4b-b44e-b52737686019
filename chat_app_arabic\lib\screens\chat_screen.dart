import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import '../models/message_model.dart';
import '../services/firebase_service.dart';
import 'package:uuid/uuid.dart';

class ChatScreen extends StatefulWidget {
  final ChatModel chat;
  final UserModel? otherUser;
  final VoidCallback? onBack;

  const ChatScreen({
    super.key,
    required this.chat,
    this.otherUser,
    this.onBack,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final Uuid _uuid = const Uuid();

  @override
  void initState() {
    super.initState();
    // تحديد الرسائل كمقروءة عند دخول المحادثة
    if (FirebaseService.currentUserId != null) {
      FirebaseService.markMessagesAsRead(
        widget.chat.id,
        FirebaseService.currentUserId!,
      );
    }
  }

  void _sendMessage(String text) {
    if (text.trim().isEmpty || FirebaseService.currentUserId == null) return;

    final message = MessageModel(
      id: _uuid.v4(),
      chatId: widget.chat.id,
      senderId: FirebaseService.currentUserId!,
      senderName: 'محمد أحمد', // يمكن الحصول عليه من المستخدم الحالي
      content: text.trim(),
      type: MessageType.text,
      timestamp: DateTime.now(),
      isRead: false,
      readBy: [FirebaseService.currentUserId!],
    );

    FirebaseService.sendMessage(message);
    _controller.clear();
    _scrollToBottom();
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent + 100,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB))),
            ),
            child: Row(
              children: [
                if (widget.onBack != null)
                  IconButton(
                    icon: const Icon(
                      FontAwesomeIcons.arrowRight,
                      color: Colors.grey,
                    ),
                    onPressed: widget.onBack,
                  ),
                CircleAvatar(
                  backgroundImage: NetworkImage(
                    widget.otherUser?.avatarUrl ??
                        'https://ui-avatars.com/api/?name=مستخدم&background=6366f1&color=fff',
                  ),
                  radius: 20,
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.otherUser?.name ?? 'مستخدم',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      widget.otherUser?.isOnline == true ? 'متصل الآن' : 'غير متصل',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.phone, color: Colors.grey),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.video, color: Colors.grey),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.ellipsisVertical,
                    color: Colors.grey,
                  ),
                  onPressed: () {},
                ),
              ],
            ),
          ),
          // Messages
          Expanded(
            child: Container(
              color: const Color(0xFFF3F4F6),
              child: StreamBuilder<List<MessageModel>>(
                stream: FirebaseService.getChatMessages(widget.chat.id),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text('خطأ في تحميل الرسائل: ${snapshot.error}'),
                    );
                  }

                  List<MessageModel> messages = snapshot.data ?? [];

                  if (messages.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            FontAwesomeIcons.comment,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'لا توجد رسائل بعد',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'ابدأ المحادثة بإرسال رسالة',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // التمرير التلقائي عند وصول رسائل جديدة
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToBottom();
                  });

                  return ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: messages.length,
                    itemBuilder: (context, index) {
                      final message = messages[index];
                      final isSent = message.senderId == FirebaseService.currentUserId;

                      return Align(
                        alignment: isSent ? Alignment.centerLeft : Alignment.centerRight,
                        child: Row(
                          mainAxisAlignment: isSent
                              ? MainAxisAlignment.start
                              : MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            if (!isSent) ...[
                              CircleAvatar(
                                backgroundImage: NetworkImage(
                                  widget.otherUser?.avatarUrl ??
                                      'https://ui-avatars.com/api/?name=${message.senderName}&background=6366f1&color=fff',
                                ),
                                radius: 16,
                              ),
                              const SizedBox(width: 8),
                            ],
                            Flexible(
                              child: Container(
                                margin: const EdgeInsets.symmetric(vertical: 4),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                decoration: BoxDecoration(
                                  color: isSent ? Colors.blue : Colors.white,
                                  borderRadius: BorderRadius.only(
                                    topLeft: const Radius.circular(24),
                                    topRight: const Radius.circular(24),
                                    bottomLeft: isSent
                                        ? const Radius.circular(8)
                                        : const Radius.circular(24),
                                    bottomRight: isSent
                                        ? const Radius.circular(24)
                                        : const Radius.circular(8),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      message.content,
                                      style: TextStyle(
                                        color: isSent ? Colors.white : Colors.black87,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _formatMessageTime(message.timestamp),
                                      style: TextStyle(
                                        color: isSent 
                                            ? Colors.white.withOpacity(0.7)
                                            : Colors.grey,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            if (isSent) const SizedBox(width: 8),
                          ],
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
          // Input
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.circlePlus,
                    color: Colors.grey,
                  ),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.image, color: Colors.grey),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.microphone,
                    color: Colors.grey,
                  ),
                  onPressed: () {},
                ),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      hintText: "اكتب رسالة...",
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 16),
                    ),
                    onSubmitted: _sendMessage,
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.solidFaceSmile,
                    color: Colors.grey,
                  ),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.paperPlane,
                    color: Colors.blue,
                  ),
                  onPressed: () => _sendMessage(_controller.text),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatMessageTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
