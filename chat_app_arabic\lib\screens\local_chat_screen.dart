import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import '../models/message_model.dart';
import '../services/local_data_service.dart';
import 'package:uuid/uuid.dart';

class LocalChatScreen extends StatefulWidget {
  final ChatModel chat;
  final UserModel? otherUser;
  final VoidCallback? onBack;

  const LocalChatScreen({
    super.key,
    required this.chat,
    this.otherUser,
    this.onBack,
  });

  @override
  State<LocalChatScreen> createState() => _LocalChatScreenState();
}

class _LocalChatScreenState extends State<LocalChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final Uuid _uuid = const Uuid();
  List<MessageModel> messages = [];

  @override
  void initState() {
    super.initState();
    _loadMessages();
    // تحديد الرسائل كمقروءة عند دخول المحادثة
    LocalDataService.markMessagesAsRead(
      widget.chat.id,
      LocalDataService.currentUserId,
    );
  }

  void _loadMessages() {
    setState(() {
      messages = LocalDataService.getChatMessages(widget.chat.id);
    });
    _scrollToBottom();
  }

  void _sendMessage(String text) {
    if (text.trim().isEmpty) return;

    final message = MessageModel(
      id: _uuid.v4(),
      chatId: widget.chat.id,
      senderId: LocalDataService.currentUserId,
      senderName: 'محمد أحمد',
      content: text.trim(),
      type: MessageType.text,
      timestamp: DateTime.now(),
      isRead: false,
      readBy: [LocalDataService.currentUserId],
    );

    LocalDataService.sendMessage(message);
    _controller.clear();
    _loadMessages();

    // محاكاة رد تلقائي
    _simulateReply();
  }

  void _simulateReply() {
    Future.delayed(const Duration(seconds: 2), () {
      final replies = [
        "حسناً، شكراً لك",
        "هل يمكنك توضيح ذلك أكثر؟",
        "سأرد عليك لاحقاً",
        "أتفهم ما تقصده",
        "هذا رائع!",
        "موافق",
        "سأعمل على ذلك",
      ];

      final randomReply = replies[DateTime.now().millisecond % replies.length];

      final replyMessage = MessageModel(
        id: _uuid.v4(),
        chatId: widget.chat.id,
        senderId: widget.otherUser?.id ?? 'unknown',
        senderName: widget.otherUser?.name ?? 'مستخدم',
        content: randomReply,
        type: MessageType.text,
        timestamp: DateTime.now(),
        isRead: false,
        readBy: [widget.otherUser?.id ?? 'unknown'],
      );

      LocalDataService.sendMessage(replyMessage);
      if (mounted) {
        _loadMessages();
      }
    });
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent + 100,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB))),
            ),
            child: Row(
              children: [
                if (widget.onBack != null)
                  IconButton(
                    icon: const Icon(
                      FontAwesomeIcons.arrowRight,
                      color: Colors.grey,
                    ),
                    onPressed: widget.onBack,
                  ),
                CircleAvatar(
                  backgroundImage: NetworkImage(
                    widget.otherUser?.avatarUrl ??
                        'https://ui-avatars.com/api/?name=مستخدم&background=6366f1&color=fff',
                  ),
                  radius: 20,
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.otherUser?.name ?? 'مستخدم',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      widget.otherUser?.isOnline == true
                          ? 'متصل الآن'
                          : 'غير متصل',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.phone, color: Colors.grey),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة المكالمات ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.video, color: Colors.grey),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة مكالمات الفيديو ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.ellipsisVertical,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    _showOptionsMenu();
                  },
                ),
              ],
            ),
          ),
          // Messages
          Expanded(
            child: Container(
              color: const Color(0xFFF3F4F6),
              child:
                  messages.isEmpty
                      ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              FontAwesomeIcons.comment,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد رسائل بعد',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'ابدأ المحادثة بإرسال رسالة',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                      : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          final isSent =
                              message.senderId ==
                              LocalDataService.currentUserId;

                          return Align(
                            alignment:
                                isSent
                                    ? Alignment.centerLeft
                                    : Alignment.centerRight,
                            child: Row(
                              mainAxisAlignment:
                                  isSent
                                      ? MainAxisAlignment.start
                                      : MainAxisAlignment.end,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                if (!isSent) ...[
                                  CircleAvatar(
                                    backgroundImage: NetworkImage(
                                      widget.otherUser?.avatarUrl ??
                                          'https://ui-avatars.com/api/?name=${message.senderName}&background=6366f1&color=fff',
                                    ),
                                    radius: 16,
                                  ),
                                  const SizedBox(width: 8),
                                ],
                                Flexible(
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(
                                      vertical: 4,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isSent ? Colors.blue : Colors.white,
                                      borderRadius: BorderRadius.only(
                                        topLeft: const Radius.circular(24),
                                        topRight: const Radius.circular(24),
                                        bottomLeft:
                                            isSent
                                                ? const Radius.circular(8)
                                                : const Radius.circular(24),
                                        bottomRight:
                                            isSent
                                                ? const Radius.circular(24)
                                                : const Radius.circular(8),
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          message.content,
                                          style: TextStyle(
                                            color:
                                                isSent
                                                    ? Colors.white
                                                    : Colors.black87,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          _formatMessageTime(message.timestamp),
                                          style: TextStyle(
                                            color:
                                                isSent
                                                    ? Colors.white.withOpacity(
                                                      0.7,
                                                    )
                                                    : Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (isSent) const SizedBox(width: 8),
                              ],
                            ),
                          );
                        },
                      ),
            ),
          ),
          // Input
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.circlePlus,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة إرفاق الملفات ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(FontAwesomeIcons.image, color: Colors.grey),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة إرسال الصور ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.microphone,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة الرسائل الصوتية ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      hintText: "اكتب رسالة...",
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 16),
                    ),
                    onSubmitted: _sendMessage,
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.solidFaceSmile,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة الرموز التعبيرية ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.paperPlane,
                    color: Colors.blue,
                  ),
                  onPressed: () => _sendMessage(_controller.text),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(FontAwesomeIcons.user),
                  title: const Text('معلومات المحادثة'),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إنشاء قاعدة البيانات بنجاح!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(FontAwesomeIcons.bell),
                  title: const Text('إعدادات الإشعارات'),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة الإشعارات ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    FontAwesomeIcons.trash,
                    color: Colors.red,
                  ),
                  title: const Text(
                    'حذف المحادثة',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('وظيفة حذف المحادثة ستتوفر قريباً'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  String _formatMessageTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
