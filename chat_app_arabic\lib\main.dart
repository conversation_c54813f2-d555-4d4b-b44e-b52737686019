import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

void main() {
  runApp(const ChatApp());
}

class ChatApp extends StatelessWidget {
  const ChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق المراسلة',
      theme: ThemeData(fontFamily: 'Tajawal', primarySwatch: Colors.blue),
      debugShowCheckedModeBanner: false,
      home: const ChatHomePage(),
      locale: const Locale('ar', 'EG'),
      supportedLocales: const [Locale('ar', 'EG')],
    );
  }
}

// بيانات المحادثات الأولية
class Chat {
  final String name;
  final String lastMessage;
  final String time;
  final String avatarUrl;
  final bool online;
  final int unread;
  final List<Message> messages;

  Chat({
    required this.name,
    required this.lastMessage,
    required this.time,
    required this.avatarUrl,
    required this.online,
    required this.unread,
    required this.messages,
  });
}

class Message {
  final String text;
  final bool sent;
  final DateTime time;

  Message({required this.text, required this.sent, required this.time});
}

// ... سيتم إضافة واجهة المستخدم لاحقاً ...

final List<Chat> chats = [
  Chat(
    name: 'أحمد خالد',
    lastMessage: 'نعم، سأكون هناك في الموعد المحدد',
    time: '10:30 ص',
    avatarUrl:
        'https://ui-avatars.com/api/?name=أحمد&background=10b981&color=fff',
    online: true,
    unread: 0,
    messages: [
      Message(
        text: 'مرحباً محمد، كيف حالك؟',
        sent: false,
        time: DateTime.now().subtract(const Duration(minutes: 10)),
      ),
      Message(
        text: 'هل انتهيت من العمل على المشروع؟',
        sent: false,
        time: DateTime.now().subtract(const Duration(minutes: 9)),
      ),
      Message(
        text: 'مرحباً أحمد، أنا بخير الحمد لله',
        sent: true,
        time: DateTime.now().subtract(const Duration(minutes: 8)),
      ),
      Message(
        text: 'نعم، لقد انتهيت منه البارحة',
        sent: true,
        time: DateTime.now().subtract(const Duration(minutes: 7)),
      ),
      Message(
        text: 'هذا رائع! هل يمكنك إرسال الملفات لي؟',
        sent: false,
        time: DateTime.now().subtract(const Duration(minutes: 6)),
      ),
      Message(
        text: 'بالطبع، سأرسلها لك خلال ساعة',
        sent: true,
        time: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
    ],
  ),
  Chat(
    name: 'سارة محمد',
    lastMessage: 'شكراً لك على المساعدة',
    time: 'أمس',
    avatarUrl:
        'https://ui-avatars.com/api/?name=سارة&background=a855f7&color=fff',
    online: false,
    unread: 0,
    messages: [
      Message(
        text: 'شكراً لك على المساعدة',
        sent: false,
        time: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ],
  ),
  Chat(
    name: 'علي حسن',
    lastMessage: 'هل انتهيت من المهمة؟',
    time: 'الثلاثاء',
    avatarUrl:
        'https://ui-avatars.com/api/?name=علي&background=f59e0b&color=fff',
    online: true,
    unread: 0,
    messages: [
      Message(
        text: 'هل انتهيت من المهمة؟',
        sent: false,
        time: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ],
  ),
  Chat(
    name: 'نور عبدالله',
    lastMessage: 'سأرسل لك الملفات لاحقاً',
    time: 'الإثنين',
    avatarUrl:
        'https://ui-avatars.com/api/?name=نور&background=ec4899&color=fff',
    online: false,
    unread: 0,
    messages: [
      Message(
        text: 'سأرسل لك الملفات لاحقاً',
        sent: false,
        time: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ],
  ),
  Chat(
    name: 'يوسف إبراهيم',
    lastMessage: 'مرحباً، كيف حالك؟',
    time: 'الأحد',
    avatarUrl:
        'https://ui-avatars.com/api/?name=يوسف&background=6366f1&color=fff',
    online: false,
    unread: 3,
    messages: [
      Message(
        text: 'مرحباً، كيف حالك؟',
        sent: false,
        time: DateTime.now().subtract(const Duration(days: 4)),
      ),
    ],
  ),
];

class ChatHomePage extends StatefulWidget {
  const ChatHomePage({super.key});

  @override
  State<ChatHomePage> createState() => _ChatHomePageState();
}

class _ChatHomePageState extends State<ChatHomePage> {
  int selectedChat = 0;

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width >= 900;
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Row(
          children: [
            // Sidebar
            if (isLargeScreen)
              SizedBox(
                width: 320,
                child: Sidebar(
                  chats: chats,
                  selected: selectedChat,
                  onSelect: (i) => setState(() => selectedChat = i),
                ),
              ),
            // Main chat area
            Expanded(
              child:
                  isLargeScreen
                      ? ChatScreen(chat: chats[selectedChat])
                      : Sidebar(
                        chats: chats,
                        selected: selectedChat,
                        onSelect: (i) => setState(() => selectedChat = i),
                        showOnlyList: true,
                      ),
            ),
          ],
        ),
      ),
    );
  }
}

class Sidebar extends StatelessWidget {
  final List<Chat> chats;
  final int selected;
  final Function(int) onSelect;
  final bool showOnlyList;
  const Sidebar({
    super.key,
    required this.chats,
    required this.selected,
    required this.onSelect,
    this.showOnlyList = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          if (!showOnlyList)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB))),
                color: Colors.white,
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundImage: NetworkImage(
                      'https://ui-avatars.com/api/?name=محمد&background=3b82f6&color=fff',
                    ),
                    radius: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          'محمد أحمد',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 2),
                        Text(
                          'متصل الآن',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(
                      FontAwesomeIcons.ellipsisVertical,
                      color: Colors.grey,
                    ),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          if (!showOnlyList)
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'ابحث عن محادثة...',
                  prefixIcon: const Icon(
                    FontAwesomeIcons.magnifyingGlass,
                    color: Colors.grey,
                    size: 18,
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF3F4F6),
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 0,
                    horizontal: 16,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
          Expanded(
            child: ListView.separated(
              itemCount: chats.length,
              separatorBuilder:
                  (context, i) =>
                      const Divider(height: 1, color: Color(0xFFE5E7EB)),
              itemBuilder: (context, i) {
                final chat = chats[i];
                return Material(
                  color:
                      i == selected && !showOnlyList
                          ? const Color(0xFFF3F4F6)
                          : Colors.white,
                  child: InkWell(
                    onTap: () => onSelect(i),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 10,
                      ),
                      child: Row(
                        children: [
                          Stack(
                            children: [
                              CircleAvatar(
                                backgroundImage: NetworkImage(chat.avatarUrl),
                                radius: 24,
                              ),
                              if (chat.online)
                                Positioned(
                                  bottom: 0,
                                  right: 0,
                                  child: Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: Colors.green,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 2,
                                      ),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ),
                              if (chat.unread > 0)
                                Positioned(
                                  top: -2,
                                  left: -2,
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: const BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Text(
                                      chat.unread.toString(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      chat.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      chat.time,
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 2),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        chat.lastMessage,
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.grey,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Icon(
                                      chat.unread > 0
                                          ? FontAwesomeIcons.check
                                          : FontAwesomeIcons.checkDouble,
                                      color:
                                          chat.unread > 0
                                              ? Colors.grey
                                              : Colors.blue,
                                      size: 14,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// شاشة الدردشة سيتم إضافتها لاحقاً
class ChatScreen extends StatefulWidget {
  final Chat chat;
  const ChatScreen({super.key, required this.chat});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Message> messages = [];
  bool isTyping = false;

  @override
  void initState() {
    super.initState();
    messages = List.from(widget.chat.messages);
  }

  void sendMessage(String text) {
    if (text.trim().isEmpty) return;
    setState(() {
      messages.add(Message(text: text, sent: true, time: DateTime.now()));
      isTyping = true;
    });
    _controller.clear();
    _scrollToBottom();
    // Simulate reply
    Future.delayed(Duration(seconds: 1 + (DateTime.now().second % 3)), () {
      setState(() {
        isTyping = false;
        messages.add(
          Message(
            text:
                [
                  "حسناً، شكراً لك",
                  "هل يمكنك توضيح ذلك أكثر؟",
                  "سأرد عليك لاحقاً",
                  "أتفهم ما تقصده",
                  "هذا رائع!",
                ][DateTime.now().second % 5],
            sent: false,
            time: DateTime.now(),
          ),
        );
      });
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent + 100,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB))),
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundImage: NetworkImage(widget.chat.avatarUrl),
                radius: 20,
              ),
              const SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.chat.name,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.chat.online ? 'متصل الآن' : 'غير متصل',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(FontAwesomeIcons.phone, color: Colors.grey),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(FontAwesomeIcons.video, color: Colors.grey),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.ellipsisVertical,
                  color: Colors.grey,
                ),
                onPressed: () {},
              ),
            ],
          ),
        ),
        // Messages
        Expanded(
          child: Container(
            color: const Color(0xFFF3F4F6),
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: messages.length + (isTyping ? 1 : 0),
              itemBuilder: (context, index) {
                if (isTyping && index == messages.length) {
                  // Typing indicator
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CircleAvatar(
                        backgroundImage: NetworkImage(widget.chat.avatarUrl),
                        radius: 16,
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _dot(),
                            const SizedBox(width: 3),
                            _dot(delay: 200),
                            const SizedBox(width: 3),
                            _dot(delay: 400),
                          ],
                        ),
                      ),
                    ],
                  );
                }
                final msg = messages[index];
                final isSent = msg.sent;
                return Align(
                  alignment:
                      isSent ? Alignment.centerLeft : Alignment.centerRight,
                  child: Row(
                    mainAxisAlignment:
                        isSent
                            ? MainAxisAlignment.start
                            : MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (!isSent) ...[
                        CircleAvatar(
                          backgroundImage: NetworkImage(widget.chat.avatarUrl),
                          radius: 16,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Flexible(
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          decoration: BoxDecoration(
                            color: isSent ? Colors.blue : Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: const Radius.circular(24),
                              topRight: const Radius.circular(24),
                              bottomLeft:
                                  isSent
                                      ? const Radius.circular(8)
                                      : const Radius.circular(24),
                              bottomRight:
                                  isSent
                                      ? const Radius.circular(24)
                                      : const Radius.circular(8),
                            ),
                          ),
                          child: Text(
                            msg.text,
                            style: TextStyle(
                              color: isSent ? Colors.white : Colors.black87,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      if (isSent) const SizedBox(width: 8),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
        // Input
        Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.circlePlus,
                  color: Colors.grey,
                ),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(FontAwesomeIcons.image, color: Colors.grey),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.microphone,
                  color: Colors.grey,
                ),
                onPressed: () {},
              ),
              Expanded(
                child: TextField(
                  controller: _controller,
                  decoration: const InputDecoration(
                    hintText: "اكتب رسالة...",
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16),
                  ),
                  onSubmitted: sendMessage,
                ),
              ),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.solidFaceSmile,
                  color: Colors.grey,
                ),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.paperPlane,
                  color: Colors.blue,
                ),
                onPressed: () => sendMessage(_controller.text),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _dot({int delay = 0}) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.3, end: 1),
      duration: const Duration(milliseconds: 1200),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Opacity(opacity: value, child: child);
      },
      child: Container(
        width: 8,
        height: 8,
        margin: EdgeInsets.only(top: delay / 10),
        decoration: const BoxDecoration(
          color: Color(0xFF9CA3AF),
          shape: BoxShape.circle,
        ),
      ),
      onEnd: () {},
    );
  }
}
