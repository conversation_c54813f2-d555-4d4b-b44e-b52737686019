// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'ضع هنا apiKey من Firebase Console',
    appId: 'ضع هنا appId من Firebase Console',
    messagingSenderId: 'ضع هنا messagingSenderId من Firebase Console',
    projectId: 'ضع هنا projectId من Firebase Console',
    authDomain: 'ضع هنا authDomain من Firebase Console',
    storageBucket: 'ضع هنا storageBucket من Firebase Console',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDemo-AndroidApiKey-ForDevelopment',
    appId: '1:123456789:android:demo123456789',
    messagingSenderId: '123456789',
    projectId: 'chat-app-arabic-demo',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDemo-iOSApiKey-ForDevelopment',
    appId: '1:123456789:ios:demo123456789',
    messagingSenderId: '123456789',
    projectId: 'chat-app-arabic-demo',
    iosBundleId: 'com.example.chatAppArabic',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDemo-macOSApiKey-ForDevelopment',
    appId: '1:123456789:macos:demo123456789',
    messagingSenderId: '123456789',
    projectId: 'chat-app-arabic-demo',
    iosBundleId: 'com.example.chatAppArabic',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDemo-WindowsApiKey-ForDevelopment',
    appId: '1:123456789:windows:demo123456789',
    messagingSenderId: '123456789',
    projectId: 'chat-app-arabic-demo',
  );
}
