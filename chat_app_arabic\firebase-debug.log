[debug] [2025-07-11T18:41:04.137Z] ----------------------------------------------------------------------
[debug] [2025-07-11T18:41:04.145Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-07-11T18:41:04.146Z] CLI Version:   14.10.1
[debug] [2025-07-11T18:41:04.147Z] Platform:      win32
[debug] [2025-07-11T18:41:04.148Z] Node Version:  v22.15.0
[debug] [2025-07-11T18:41:04.149Z] Time:          Fri Jul 11 2025 21:41:04 GMT+0300 (التوقيت العربي الرسمي)
[debug] [2025-07-11T18:41:04.150Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-11T18:41:04.162Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-11T18:41:04.164Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\Desktop\nar\chat_app_arabic

