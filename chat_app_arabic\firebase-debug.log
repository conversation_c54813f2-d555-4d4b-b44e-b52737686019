[debug] [2025-07-11T19:11:00.105Z] ----------------------------------------------------------------------
[debug] [2025-07-11T19:11:00.109Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js use --add
[debug] [2025-07-11T19:11:00.109Z] CLI Version:   14.10.1
[debug] [2025-07-11T19:11:00.110Z] Platform:      win32
[debug] [2025-07-11T19:11:00.110Z] Node Version:  v22.15.0
[debug] [2025-07-11T19:11:00.110Z] Time:          Fri Jul 11 2025 22:11:00 GMT+0300 (التوقيت العربي الرسمي)
[debug] [2025-07-11T19:11:00.110Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-11T19:11:00.461Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-11T19:11:00.463Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-11T19:11:00.466Z] Checked if tokens are valid: true, expires at: 1752262667709
[debug] [2025-07-11T19:11:00.466Z] Checked if tokens are valid: true, expires at: 1752262667709
[debug] [2025-07-11T19:11:00.468Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
